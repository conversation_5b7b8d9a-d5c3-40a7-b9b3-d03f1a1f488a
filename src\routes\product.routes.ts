import { Router } from 'express';
import {
  createProduct,
  getAllProducts,
  getAllProductsAdmin,
  getProductDetails,
  getProductStock,
  updateProduct,
  deleteProduct,
  markAsSold,
  getShopProducts,
  getShopDetailsWithProducts,
  getAllProductsByCategory,
  getAllProductsBySubCategory,
  getAllUniqueColors,
  filterProducts
} from '../controllers/product.controller';
import { verifyTokenForbrandOwner, verifyTokenForUser } from '../middlewares/auth.middlewares';
import { validateProductMedia } from '../utils/validateProductMedia';

const router = Router();

//----- User Side Routes (Place specific routes FIRST)
router.get('/all-by-category', verifyTokenForUser, getAllProductsByCategory);
router.get('/all-by-sub-category', verifyTokenForUser, getAllProductsBySubCategory);
// Get all unique colors
router.get('/unique-colors', verifyTokenForUser, getAllUniqueColors);
router.get('/filters', verifyTokenForUser, filterProducts);

// Public routes - get product details and stock (Dynamic routes come AFTER)
router.get('/:productId', getProductDetails);
router.get('/:productId/stock', getProductStock);

// Protected routes - require business owner authentication
router.use(verifyTokenForbrandOwner);

// Get all products for logged in brand owner
router.get('/', getAllProducts);

// Create a new product
router.post('/', validateProductMedia, createProduct);

// Update product
router.put('/:productId', validateProductMedia, updateProduct);

// Delete product
router.delete('/:productId', deleteProduct);

// Mark product as sold
router.patch('/:productId/mark-sold', markAsSold);

// Get all products of a specific shop
router.get('/shop/:shopId', getShopProducts);

// Get shop details with product information
router.get('/shop/:shopId/details', getShopDetailsWithProducts);



export default router;