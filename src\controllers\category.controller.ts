import { Request, Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import { Types } from 'mongoose';
import Category from '../models/Category';
import { ICreateCategoryRequest, IUpdateCategoryRequest } from '../interfaces/category.interfaces';
import { validateCreateCategoryData, validateUpdateCategoryData } from '../utils/validations';
import { uploadFile } from '../utils/mediaHandling';
import { notifyCategoryCreated } from '../services/notification.service';

// Create a new category
export const createCategory = async (req: Request, res: Response) => {
  try {
    const {categoryName, categoryDescription, categoryType }: ICreateCategoryRequest = req.body;
    const files = req.files as Express.Multer.File[];

    // Validate input data
    const validation = validateCreateCategoryData({categoryName, categoryDescription, categoryType });
    if (!validation.isValid) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Validation failed',
        errors: validation.errors
      });
    }

    // Check if category name already exists
    const existingCategory = await Category.findOne({ 
      categoryName: categoryName.trim() 
    });

    if (existingCategory) {
      return res.status(StatusCodes.CONFLICT).json({
        success: false,
        message: 'Category with this name already exists'
      });
    }
    let categoryImage = '';
    if(files && files.length > 0) {
      const url = await uploadFile(files[0]);
      categoryImage = url;
    }

    // Create new category
    const newCategory = new Category({
      categoryImage: categoryImage,
      categoryName: categoryName.trim(),
      categoryDescription: categoryDescription.trim(),
      categoryType: categoryType.toLowerCase().trim()
    });

    await newCategory.save();

    // Send notifications to all brand owners about new category
    try {
      await notifyCategoryCreated(newCategory._id, newCategory.categoryName);
    } catch (notificationError) {
      console.error('Error sending category creation notifications:', notificationError);
      // Don't fail the category creation if notification fails
    }

    return res.status(StatusCodes.CREATED).json({
      success: true,
      message: 'Category created successfully',
      category: {
        id: newCategory._id,
        categoryImage: newCategory.categoryImage,
        categoryName: newCategory.categoryName,
        categoryDescription: newCategory.categoryDescription,
        categoryType: newCategory.categoryType,
        createdAt: newCategory.createdAt
      }
    });

  } catch (error) {
    console.error('Error creating category:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error creating category',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get all categories
export const getAllCategories = async (req: Request, res: Response) => {
  try {
    const { categoryType } = req.query;
    
    // Build filter object
    const filter: any = {};
    if (categoryType && typeof categoryType === 'string') {
      filter.categoryType = categoryType.toLowerCase();
    }

    const categories = await Category.find(filter)
      .sort({ categoryName: 1 })
      .select('-__v');

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Categories retrieved successfully',
      count: categories.length,
      categories: categories.map(category => ({
        id: category._id,
        categoryImage: category.categoryImage,
        categoryName: category.categoryName,
        categoryDescription: category.categoryDescription,
        categoryType: category.categoryType,
        createdAt: category.createdAt,
        updatedAt: category.updatedAt
      }))
    });

  } catch (error) {
    console.error('Error getting categories:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error retrieving categories',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get specific category details
export const getCategoryDetails = async (req: Request, res: Response) => {
  try {
    const { categoryId } = req.params;

    if (!Types.ObjectId.isValid(categoryId)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Invalid category ID'
      });
    }

    const category = await Category.findById(new Types.ObjectId(categoryId)).select('-__v');

    if (!category) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Category not found'
      });
    }

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Category details retrieved successfully',
      category: {
        id: category._id,
        categoryImage: category.categoryImage,
        categoryName: category.categoryName,
        categoryDescription: category.categoryDescription,
        categoryType: category.categoryType,
        createdAt: category.createdAt,
        updatedAt: category.updatedAt
      }
    });

  } catch (error) {
    console.error('Error getting category details:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error retrieving category details',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Update category
export const updateCategory = async (req: Request, res: Response) => {
  try {
    const { categoryId } = req.params;
    const updateData: IUpdateCategoryRequest = req.body;
    const files = req.files as Express.Multer.File[];

    if (!Types.ObjectId.isValid(categoryId)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Invalid category ID'
      });
    }

    // Validate input data
    const validation = validateUpdateCategoryData(updateData);
    if (!validation.isValid) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Validation failed',
        errors: validation.errors
      });
    }

    // Find category
    const category = await Category.findById(new Types.ObjectId(categoryId));

    if (!category) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Category not found'
      });
    }

    // Check if new category name already exists (if categoryName is being updated)
    if (updateData.categoryName && updateData.categoryName.trim() !== category.categoryName) {
      const existingCategory = await Category.findOne({ 
        categoryName: updateData.categoryName.trim(),
        _id: { $ne: new Types.ObjectId(categoryId) }
      });

      if (existingCategory) {
        return res.status(StatusCodes.CONFLICT).json({
          success: false,
          message: 'Category with this name already exists'
        });
      }
    }

    // Prepare update object
    const updateObject: any = {};
    if (updateData.categoryName) updateObject.categoryName = updateData.categoryName.trim();
    if (updateData.categoryDescription) updateObject.categoryDescription = updateData.categoryDescription.trim();
    if (updateData.categoryType) updateObject.categoryType = updateData.categoryType.toLowerCase().trim();
    if(files.length > 0) {
      const url = await uploadFile(files[0]);
      updateObject.categoryImage = url;
    }else {
      updateObject.categoryImage = category.categoryImage;
    }
    // Update category
    const updatedCategory = await Category.findByIdAndUpdate(
      new Types.ObjectId(categoryId),
      updateObject,
      { new: true, runValidators: true }
    );

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Category updated successfully',
      category: {
        id: updatedCategory!._id,
        categoryImage: updatedCategory!.categoryImage,
        categoryName: updatedCategory!.categoryName,
        categoryDescription: updatedCategory!.categoryDescription,
        categoryType: updatedCategory!.categoryType,
        updatedAt: updatedCategory!.updatedAt
      }
    });

  } catch (error) {
    console.error('Error updating category:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error updating category',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Delete category
export const deleteCategory = async (req: Request, res: Response) => {
  try {
    const { categoryId } = req.params;

    if (!Types.ObjectId.isValid(categoryId)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Invalid category ID'
      });
    }

    // Find and delete category
    const deletedCategory = await Category.findByIdAndDelete(new Types.ObjectId(categoryId));

    if (!deletedCategory) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Category not found'
      });
    }

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Category deleted successfully',
      deletedCategory: {
        id: deletedCategory._id,
        categoryName: deletedCategory.categoryName
      }
    });

  } catch (error) {
    console.error('Error deleting category:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error deleting category',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};
